This is a league based challenge.

For this challenge, multiple leagues for the same game are available. Once you have proven your skills against the first Boss, you will access a higher league and extra rules will be available.

NEW: In wooden leagues, your submission will only fight the boss in the arena. Complete the objective specified in each league at least 3 times out 5 to advance to the next league.

 	Goal
In this league, shoot the enemy agent with the highest wetness on each turn using both your agents.
 	Rules
The game is played on a grid.

Each player controls a team of agents.

Objective 2: the SHOOT action

Your agents can move! In this next league, enemy agents have entered the field!

Thankfully, your agents are also capable of performing the SHOOT action.

In this game, agents can shoot each other with water guns. Shooting an agent will increase its wetness.If an agent's wetness reaches 100 or more, they are removed from the game.

The amount of wetness added to an agent when shot is equal to the soakingPower of the shooter. This can be refered to as damage.

However, that amount will be halved if the manhattan distance separating the two agents is greater than the optimalRangeof the shooter. The shot will fail if the distance is greater than twice the optimalRange, in which case no damage is dealt.

Enemy agents will be present in the list of agents in the standard input. You may identify them with the player variable. You are also given their agentId and wetness. The agents with a value player that equals myId are yours.

The SHOOT id action will tell an agent to shoot the agent with the given id. Each agent can perform one SHOOT action per turn.

Victory Conditions
In this league you have two agents on a small grid. Your objective is to shoot the enemy agent with the highest wetness on each turn using both your agents.

Defeat Conditions
One or more of your agents does not shoot the wettest foe.
Your program does not provide a command in the alloted time or one of the commands is invalid.

Debugging tips
Hover over the grid to see extra information on the tile under your mouse.
Assign the special MESSAGE text action to an agent and that text will appear above your agent.
Press the gear icon on the viewer to access extra display options.
Use the keyboard to control the action: space to play/pause, arrows to step 1 frame at a time.

nitialization Input
First line: one integer myId, for your player identification.
Second line: one integer agentDataCount for the number of agents on the grid.

Next agentDataCount lines: The following 6 inputs for each agent:

agentId: unique id of this agent
player: id of the player owning this agent
shootCooldown: min number or turns between two shots for this agent
optimalRange: the optimal shooting range of this agent
soakingPower: the maximum wetness damage output of this agent
splashBombs: the starting amount of splash bombs available to this agent
Next line: two integers width and height for the size of the grid.

The next width * height lines: The following 3 inputs for each tile on the grid:

x: X coordinate (0 is leftmost)
y: Y coordinate (0 is uppermost)
tile_type:
0 for an empty tile
1 for a low cover
2 for a high cover
Input for one game turn
First line: one integer agentCount for the number of remaining agents on the grid.

Next agentCount lines: The following 6 inputs for each agent:

agentId: unique id of this agent
x: X coordinate (0 is leftmost)
y: Y coordinate (0 is uppermost)
cooldown: number of turns left until this agent can shoot again
splashBombs: current amount of splash bombs available to this agent
wetness: current wetness of the agent
Next line: one integer myAgentCount for the number of agents controlled by the player.

Output
A single line per agent, preceded by its agentId and followed by its action(s):

Up to one move action:
MOVE x y: Attempt to move towards the location x, y.
Up to one combat action:
SHOOT id: Attempt to shoot agent agentId.
THROW: Attempt to throw a splash bomb at the location x, y.
HUNKER_DOWN: Hunker down to gain 25% damage reduction against enemy attacks this turn.
Up to one message action:
MESSAGE text: Display text in the viewer. Useful for debugging.
Instructions are separated by semicolons. For example, consider the following line:

3;MOVE 12 3;SHOOT 5

This instructs agent 3 to move towards the coordinates (12, 3) and to shoot agent 5.

Note: The agentId at the start can be omitted. In that case, the actions are assigned to the agents in ascending order of agentId.

Constraints
Response time per turn ≤ 50ms
Response time for the first turn ≤ 1000ms