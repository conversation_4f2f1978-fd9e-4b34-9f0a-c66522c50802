This is a league based challenge.

For this challenge, multiple leagues for the same game are available. Once you have proven your skills against the first Boss, you will access a higher league and extra rules will be available.

NEW: In wooden leagues, your submission will only fight the boss in the arena. Complete the objective specified in each league at least 3 times out 5 to advance to the next league.

 	Goal
In this league, eliminate all three groups of only enemy agents with your splash bomb supply.
 	Rules
The game is played on a grid.

Each player controls a team of agents.

Objective 4: Throwing splash bombs

Your agents can now run and gun behind cover! In this new league, throw splash bombs at enemies to deal massive wetness damage regardless of cover.


Agents will sometimes start the game with a number of splash bombs. The current amount of splash bombs for any given agent is given each turn in the standard input as the splashBombs variable.


Throwing a splash bomb is a combat action. Meaning it can be used after a MOVE action, just like the SHOOT action.


An agent using the THROW x y action will attempt to throw a splash bomb at the location x, y. Splash bombs can only be thrown at a maximum distance of 4 tiles away from the agent. They deal 30 wetness to the tile it lands on, and 30 wetness to all adjacent tiles (orthogonally and diagonally).

Victory Conditions
In this league, there are four groups of barricaded agents, one of which includes one of your own agents. You must eliminate all three groups of only enemy agents with your limited splash bomb supply. Shooting is disabled.
Defeat Conditions
You hit any of your own agents.
40 turns have passed.
Your program does not provide a command in the alloted time or one of the commands is invalid.

🐞 Debugging tips
Hover over the grid to see extra information on the tile under your mouse.
Assign the special MESSAGE text action to an agent and that text will appear above your agent.
Press the gear icon on the viewer to access extra display options.
Use the keyboard to control the action: space to play/pause, arrows to step 1 frame at a time.

	Game Protocol
Initialization Input
First line: one integer myId, for your player identification.
Second line: one integer agentDataCount for the number of agents on the grid.

Next agentDataCount lines: The following 6 inputs for each agent:

agentId: unique id of this agent
player: id of the player owning this agent
shootCooldown: min number or turns between two shots for this agent
optimalRange: the optimal shooting range of this agent
soakingPower: the maximum wetness damage output of this agent
splashBombs: the starting amount of splash bombs available to this agent
Next line: two integers width and height for the size of the grid.

The next width * height lines: The following 3 inputs for each tile on the grid:

x: X coordinate (0 is leftmost)
y: Y coordinate (0 is uppermost)
tile_type:
0 for an empty tile
1 for a low cover
2 for a high cover
Input for one game turn
First line: one integer agentCount for the number of remaining agents on the grid.

Next agentCount lines: The following 6 inputs for each agent:

agentId: unique id of this agent
x: X coordinate (0 is leftmost)
y: Y coordinate (0 is uppermost)
cooldown: number of turns left until this agent can shoot again
splashBombs: current amount of splash bombs available to this agent
wetness: current wetness of the agent
Next line: one integer myAgentCount for the number of agents controlled by the player.

Output
A single line per agent, preceded by its agentId and followed by its action(s):

Up to one move action:
MOVE x y: Attempt to move towards the location x, y.
Up to one combat action:
SHOOT id: Attempt to shoot agent agentId.
THROW: Attempt to throw a splash bomb at the location x, y.
HUNKER_DOWN: Hunker down to gain 25% damage reduction against enemy attacks this turn.
Up to one message action:
MESSAGE text: Display text in the viewer. Useful for debugging.
Instructions are separated by semicolons. For example, consider the following line:

3;MOVE 12 3;SHOOT 5

This instructs agent 3 to move towards the coordinates (12, 3) and to shoot agent 5.

Note: The agentId at the start can be omitted. In that case, the actions are assigned to the agents in ascending order of agentId.

Constraints
Response time per turn ≤ 50ms
Response time for the first turn ≤ 1000ms