package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
)

/**
 * Win the water fight by controlling the most territory, or out-soak your opponent!
 **/
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func manhattanDistance(x1, y1, x2, y2 int) int {
	return abs(x1-x2) + abs(y1-y2)
}

type agent struct {
	AgentID       int
	Wetness       int
	PlayerID      int
	ShootCooldown int
	SoakingPower  int
	SplashBombs   int
	me            bool
	OptimalRange  int
	x, y          int
	cooldown      int // Current cooldown for this turn
}

func (a agent) String() string {
	return fmt.Sprintf(
		"AgentID: %d\nWetness: %d\nPlayerID: %d\nShootCooldown: %d\nSoakingPower: %d\nSplashBombs: %d\nOptimalRange: %d\n x %d y %d\nMe: %v\nCooldown: %d\n",
		a.<PERSON>ID, a.<PERSON>ness, a.<PERSON>ID, a.ShootCooldown, a.<PERSON>, a.<PERSON>, a.<PERSON>ti<PERSON><PERSON>, a.x, a.y, a.me, a.cooldown)
}

// Calculate effective damage at given distance with cover protection
func (a agent) calculateDamage(distance int, coverProtection float64) int {
	if distance > 2*a.OptimalRange {
		return 0 // Shot fails
	}
	baseDamage := a.SoakingPower
	if distance > a.OptimalRange {
		baseDamage = a.SoakingPower / 2 // Half damage beyond optimal range
	}
	// Apply cover protection
	finalDamage := float64(baseDamage) * (1.0 - coverProtection)
	return int(finalDamage)
}

// Check if agent can shoot (cooldown is 0)
func (a agent) canShoot() bool {
	return a.cooldown == 0
}

// Get cover protection percentage based on tile type
func getCoverProtection(tileType int) float64 {
	switch tileType {
	case 1: // Low cover
		return 0.5 // 50% protection
	case 2: // High cover
		return 0.75 // 75% protection
	default:
		return 0.0 // No protection
	}
}

// Check if position is orthogonally adjacent to a cover tile
func isAdjacentToCover(x, y int, gameMap [][]int, width, height int) (bool, int) {
	directions := [][]int{{0, 1}, {0, -1}, {1, 0}, {-1, 0}} // up, down, right, left
	maxCover := 0
	found := false

	for _, dir := range directions {
		nx, ny := x+dir[0], y+dir[1]
		if nx >= 0 && nx < width && ny >= 0 && ny < height {
			if gameMap[ny][nx] > 0 { // Cover tile
				found = true
				if gameMap[ny][nx] > maxCover {
					maxCover = gameMap[ny][nx]
				}
			}
		}
	}
	return found, maxCover
}

// Find best cover position for an agent
func findBestCoverPosition(agent agent, gameMap [][]int, width, height int) (int, int, int) {
	bestX, bestY := agent.x, agent.y
	bestCover := 0

	// First check current position
	if adjacent, coverType := isAdjacentToCover(agent.x, agent.y, gameMap, width, height); adjacent {
		bestCover = coverType
	}

	// Check all positions within 1 move (orthogonal only)
	directions := [][]int{{0, 1}, {0, -1}, {1, 0}, {-1, 0}} // up, down, right, left
	for _, dir := range directions {
		newX, newY := agent.x+dir[0], agent.y+dir[1]
		if newX >= 0 && newX < width && newY >= 0 && newY < height && gameMap[newY][newX] == 0 {
			// Check if this position is adjacent to cover
			if adjacent, coverType := isAdjacentToCover(newX, newY, gameMap, width, height); adjacent {
				if coverType > bestCover {
					bestCover = coverType
					bestX, bestY = newX, newY
				}
			}
		}
	}
	return bestX, bestY, bestCover
}

// Find best cover position for an agent avoiding taken positions
func findBestCoverPositionAvoidingTaken(agent agent, gameMap [][]int, width, height int, takenPositions map[string]bool) (int, int, int) {
	bestX, bestY := agent.x, agent.y
	bestCover := 0

	// First check current position if not taken
	posKey := fmt.Sprintf("%d,%d", agent.x, agent.y)
	if !takenPositions[posKey] {
		if adjacent, coverType := isAdjacentToCover(agent.x, agent.y, gameMap, width, height); adjacent {
			bestCover = coverType
		}
	}

	// Check all positions within 1 move (orthogonal only)
	directions := [][]int{{0, 1}, {0, -1}, {1, 0}, {-1, 0}} // up, down, right, left
	for _, dir := range directions {
		newX, newY := agent.x+dir[0], agent.y+dir[1]
		posKey := fmt.Sprintf("%d,%d", newX, newY)
		if newX >= 0 && newX < width && newY >= 0 && newY < height &&
		   gameMap[newY][newX] == 0 && !takenPositions[posKey] {
			// Check if this position is adjacent to cover
			if adjacent, coverType := isAdjacentToCover(newX, newY, gameMap, width, height); adjacent {
				if coverType > bestCover {
					bestCover = coverType
					bestX, bestY = newX, newY
				}
			}
		}
	}
	return bestX, bestY, bestCover
}

func main() {
	scanner := bufio.NewScanner(os.Stdin)
	scanner.Buffer(make([]byte, 1000000), 1000000)
	var inputs []string

	// myId: Your player id (0 or 1)
	var myId int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &myId)

	// agentDataCount: Total number of agents in the game
	var agentDataCount int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &agentDataCount)

	Agents := make(map[int]agent)

	for i := 0; i < agentDataCount; i++ {
		// agentId: Unique identifier for this agent
		// player: Player id of this agent
		// shootCooldown: Number of turns between each of this agent's shots
		// optimalRange: Maximum manhattan distance for greatest damage output
		// soakingPower: Damage output within optimal conditions
		// splashBombs: Number of splash bombs this can throw this game
		var agentId, player, shootCooldown, optimalRange, soakingPower, splashBombs int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentId, &player, &shootCooldown, &optimalRange, &soakingPower, &splashBombs)
		Agents[agentId] = agent{
			AgentID:       agentId,
			me:            myId == player,
			ShootCooldown: shootCooldown,
			SoakingPower:  soakingPower,
			SplashBombs:   splashBombs,
			PlayerID:      player,
			OptimalRange:  optimalRange,
		}
	}

	// width: Width of the game map
	// height: Height of the game map
	var width, height int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &width, &height)

	// Store map information (could be useful for advanced strategies)
	gameMap := make([][]int, height)
	for i := 0; i < height; i++ {
		gameMap[i] = make([]int, width)
		scanner.Scan()
		inputs = strings.Split(scanner.Text(), " ")
		for j := 0; j < width; j++ {
			x, _ := strconv.ParseInt(inputs[3*j], 10, 32)
			y, _ := strconv.ParseInt(inputs[3*j+1], 10, 32)
			tileType, _ := strconv.ParseInt(inputs[3*j+2], 10, 32)
			gameMap[int(y)][int(x)] = int(tileType)
		}
	}
	for {
		// agentCount: Total number of agents still in the game
		var agentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentCount)

		// Clear and rebuild the alive agents map
		aliveAgents := make(map[int]agent)

		// Update agent positions and states - only for agents that are still alive
		for i := 0; i < agentCount; i++ {
			var agentId, x, y, cooldown, splashBombs, wetness int
			scanner.Scan()
			fmt.Sscan(scanner.Text(), &agentId, &x, &y, &cooldown, &splashBombs, &wetness)

			if baseAgent, exists := Agents[agentId]; exists {
				// Update the agent with current turn data
				updatedAgent := baseAgent
				updatedAgent.x = x
				updatedAgent.y = y
				updatedAgent.cooldown = cooldown
				updatedAgent.SplashBombs = splashBombs
				updatedAgent.Wetness = wetness
				aliveAgents[agentId] = updatedAgent
			}
		}

		// myAgentCount: Number of alive agents controlled by you
		var myAgentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &myAgentCount)

		// Get my agents (only alive ones)
		myAgents := []agent{}
		for _, a := range aliveAgents {
			if a.me {
				myAgents = append(myAgents, a)
			}
		}

		// Get enemy agents (only alive ones)
		enemyAgents := []agent{}
		for _, a := range aliveAgents {
			if !a.me {
				enemyAgents = append(enemyAgents, a)
			}
		}

		// Debug: Log agent counts and positions
		fmt.Fprintf(os.Stderr, "Turn: MyAgents=%d, EnemyAgents=%d, TotalAlive=%d\n", len(myAgents), len(enemyAgents), len(aliveAgents))
		for _, a := range myAgents {
			fmt.Fprintf(os.Stderr, "MyAgent %d at (%d,%d)\n", a.AgentID, a.x, a.y)
		}
		for _, a := range enemyAgents {
			adjacent, coverType := isAdjacentToCover(a.x, a.y, gameMap, width, height)
			protection := 0.0
			if adjacent {
				protection = getCoverProtection(coverType)
			}
			fmt.Fprintf(os.Stderr, "Enemy %d at (%d,%d) cover:%d protection:%.0f%%\n", a.AgentID, a.x, a.y, coverType, protection*100)
		}

		// NEW STRATEGY: Move to best cover and shoot enemy with least protection
		// Track which enemies have been targeted to avoid duplicates
		targetedEnemies := make(map[int]bool)
		// Track which positions have been taken to avoid collisions
		takenPositions := make(map[string]bool)

		// Process each of my agents
		for i := 0; i < myAgentCount && i < len(myAgents); i++ {
			my := myAgents[i]
			var actions []string

			// Step 1: Find best cover position for this agent (avoiding taken positions)
			bestX, bestY, bestCover := findBestCoverPositionAvoidingTaken(my, gameMap, width, height, takenPositions)

			// Mark this position as taken
			posKey := fmt.Sprintf("%d,%d", bestX, bestY)
			takenPositions[posKey] = true

			// Step 2: Find the 2 closest enemies, then shoot the one with least protection
			// According to challenge: "shoot the opposing enemy with the least protection from cover (of the two closest enemies)"
			var target agent
			minProtection := 1.1 // Start higher than maximum protection
			found := false

			type enemyWithProtection struct {
				agent      agent
				protection float64
				distance   int
			}
			var allEnemiesWithData []enemyWithProtection

			// Get all enemies with their data
			for _, enemy := range enemyAgents {
				if _, exists := aliveAgents[enemy.AgentID]; exists && !targetedEnemies[enemy.AgentID] {
					// Calculate distance from our best cover position
					distance := manhattanDistance(bestX, bestY, enemy.x, enemy.y)

					// Check enemy's cover protection
					adjacent, coverType := isAdjacentToCover(enemy.x, enemy.y, gameMap, width, height)
					protection := 0.0
					if adjacent {
						protection = getCoverProtection(coverType)
					}
					allEnemiesWithData = append(allEnemiesWithData, enemyWithProtection{enemy, protection, distance})
				}
			}

			// Sort by distance to find the 2 closest
			if len(allEnemiesWithData) >= 1 {
				// Simple bubble sort by distance (for small arrays)
				for i := 0; i < len(allEnemiesWithData)-1; i++ {
					for j := 0; j < len(allEnemiesWithData)-i-1; j++ {
						if allEnemiesWithData[j].distance > allEnemiesWithData[j+1].distance {
							allEnemiesWithData[j], allEnemiesWithData[j+1] = allEnemiesWithData[j+1], allEnemiesWithData[j]
						}
					}
				}

				// Take the 2 closest enemies (or just 1 if only 1 available)
				closestCount := 2
				if len(allEnemiesWithData) < 2 {
					closestCount = len(allEnemiesWithData)
				}

				// Among the closest enemies, find the one with least protection that's in range
				fmt.Fprintf(os.Stderr, "Agent %d closest enemies: ", my.AgentID)
				for i := 0; i < closestCount; i++ {
					ep := allEnemiesWithData[i]
					fmt.Fprintf(os.Stderr, "E%d(D%d,P%.0f%%) ", ep.agent.AgentID, ep.distance, ep.protection*100)
					if ep.distance <= 2*my.OptimalRange && ep.protection < minProtection {
						minProtection = ep.protection
						target = ep.agent
						found = true
					}
				}
				fmt.Fprintf(os.Stderr, "\n")
			}

			if !found {
				fmt.Printf("%d;MESSAGE No valid targets\n", my.AgentID)
				continue
			}

			// Mark this enemy as targeted
			targetedEnemies[target.AgentID] = true

			// Step 3: Move to best cover position (if different from current)
			if bestX != my.x || bestY != my.y {
				actions = append(actions, fmt.Sprintf("MOVE %d %d", bestX, bestY))
			}

			// Step 4: Shoot the target with least protection
			if my.canShoot() {
				// Calculate distance from new position to target
				distance := manhattanDistance(bestX, bestY, target.x, target.y)
				if distance <= 2*my.OptimalRange {
					actions = append(actions, fmt.Sprintf("SHOOT %d", target.AgentID))
				}
			}

			// Debug message with more info
			coverProtection := getCoverProtection(bestCover)
			targetProtection := minProtection
			distance := manhattanDistance(bestX, bestY, target.x, target.y)
			actions = append(actions, fmt.Sprintf("MESSAGE A%d->(%d,%d) C%d(%.0f%%) T%d(%.0f%%) D%d R%d",
				my.AgentID, bestX, bestY, bestCover, coverProtection*100, target.AgentID, targetProtection*100,
				distance, my.OptimalRange*2))

			// Output the command
			if len(actions) > 0 {
				fmt.Printf("%d;%s\n", my.AgentID, strings.Join(actions, ";"))
			} else {
				fmt.Printf("%d;MESSAGE No actions\n", my.AgentID)
			}
		}
	}
}
