package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
)

/**
 * Win the water fight by controlling the most territory, or out-soak your opponent!
 **/
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func manhattanDistance(x1, y1, x2, y2 int) int {
	return abs(x1-x2) + abs(y1-y2)
}

type agent struct {
	AgentID       int
	Wetness       int
	PlayerID      int
	ShootCooldown int
	SoakingPower  int
	SplashBombs   int
	me            bool
	OptimalRange  int
	x, y          int
	cooldown      int // Current cooldown for this turn
}

func (a agent) String() string {
	return fmt.Sprintf(
		"AgentID: %d\nWetness: %d\nPlayerID: %d\nShootCooldown: %d\nSoakingPower: %d\nSplashBombs: %d\nOptimalRange: %d\n x %d y %d\nMe: %v\nCooldown: %d\n",
		a.AgentID, a.<PERSON>ness, a.PlayerID, a.ShootCooldown, a.<PERSON><PERSON>, a.<PERSON><PERSON>, a.Opti<PERSON>R<PERSON><PERSON>, a.x, a.y, a.me, a.cooldown)
}

// Calculate effective damage at given distance
func (a agent) calculateDamage(distance int) int {
	if distance > 2*a.OptimalRange {
		return 0 // Shot fails
	}
	if distance <= a.OptimalRange {
		return a.SoakingPower // Full damage
	}
	return a.SoakingPower / 2 // Half damage
}

// Check if agent can shoot (cooldown is 0)
func (a agent) canShoot() bool {
	return a.cooldown == 0
}

func main() {
	scanner := bufio.NewScanner(os.Stdin)
	scanner.Buffer(make([]byte, 1000000), 1000000)
	var inputs []string

	// myId: Your player id (0 or 1)
	var myId int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &myId)

	// agentDataCount: Total number of agents in the game
	var agentDataCount int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &agentDataCount)

	Agents := make(map[int]agent)

	for i := 0; i < agentDataCount; i++ {
		// agentId: Unique identifier for this agent
		// player: Player id of this agent
		// shootCooldown: Number of turns between each of this agent's shots
		// optimalRange: Maximum manhattan distance for greatest damage output
		// soakingPower: Damage output within optimal conditions
		// splashBombs: Number of splash bombs this can throw this game
		var agentId, player, shootCooldown, optimalRange, soakingPower, splashBombs int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentId, &player, &shootCooldown, &optimalRange, &soakingPower, &splashBombs)
		Agents[agentId] = agent{
			AgentID:       agentId,
			me:            myId == player,
			ShootCooldown: shootCooldown,
			SoakingPower:  soakingPower,
			SplashBombs:   splashBombs,
			PlayerID:      player,
			OptimalRange:  optimalRange,
		}
	}

	// width: Width of the game map
	// height: Height of the game map
	var width, height int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &width, &height)

	// Store map information (could be useful for advanced strategies)
	gameMap := make([][]int, height)
	for i := 0; i < height; i++ {
		gameMap[i] = make([]int, width)
		scanner.Scan()
		inputs = strings.Split(scanner.Text(), " ")
		for j := 0; j < width; j++ {
			x, _ := strconv.ParseInt(inputs[3*j], 10, 32)
			y, _ := strconv.ParseInt(inputs[3*j+1], 10, 32)
			tileType, _ := strconv.ParseInt(inputs[3*j+2], 10, 32)
			gameMap[int(y)][int(x)] = int(tileType)
		}
	}
	for {
		// agentCount: Total number of agents still in the game
		var agentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentCount)

		// Update agent positions and states
		for i := 0; i < agentCount; i++ {
			var agentId, x, y, cooldown, splashBombs, wetness int
			scanner.Scan()
			fmt.Sscan(scanner.Text(), &agentId, &x, &y, &cooldown, &splashBombs, &wetness)

			if agent, exists := Agents[agentId]; exists {
				agent.x = x
				agent.y = y
				agent.cooldown = cooldown
				agent.SplashBombs = splashBombs
				agent.Wetness = wetness
				Agents[agentId] = agent
			}
		}

		// myAgentCount: Number of alive agents controlled by you
		var myAgentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &myAgentCount)

		// Get my agents
		myAgents := []agent{}
		for _, a := range Agents {
			if a.me && a.Wetness < 100 {
				myAgents = append(myAgents, a)
			}
		}

		// Get enemy agents
		enemyAgents := []agent{}
		for _, a := range Agents {
			if !a.me && a.Wetness < 100 {
				enemyAgents = append(enemyAgents, a)
			}
		}

		// Process each of my agents
		for i := 0; i < myAgentCount && i < len(myAgents); i++ {
			my := myAgents[i]

			// Find the enemy with the highest wetness that we can potentially shoot
			var target agent
			maxWetness := -1
			found := false

			for _, enemy := range enemyAgents {
				if enemy.Wetness > maxWetness {
					maxWetness = enemy.Wetness
					target = enemy
					found = true
				}
			}

			if !found {
				// No enemies found, just output a basic command
				fmt.Printf("%d;MESSAGE No targets\n", my.AgentID)
				continue
			}

			// Calculate distance to target
			distance := manhattanDistance(my.x, my.y, target.x, target.y)

			var actions []string

			// Movement strategy: get closer to target if we're too far or not in optimal range
			if distance > my.OptimalRange {
				// Move towards the target
				moveX, moveY := my.x, my.y
				if target.x > my.x {
					moveX++
				} else if target.x < my.x {
					moveX--
				}
				if target.y > my.y {
					moveY++
				} else if target.y < my.y {
					moveY--
				}
				actions = append(actions, fmt.Sprintf("MOVE %d %d", moveX, moveY))
			}

			// Shooting strategy: shoot if we can and target is in range
			if my.canShoot() && distance <= 2*my.OptimalRange {
				actions = append(actions, fmt.Sprintf("SHOOT %d", target.AgentID))
			}

			// Add debug message
			damage := my.calculateDamage(distance)
			actions = append(actions, fmt.Sprintf("MESSAGE T:%d W:%d D:%d Dmg:%d", target.AgentID, target.Wetness, distance, damage))

			// Output the command
			if len(actions) > 0 {
				fmt.Printf("%d;%s\n", my.AgentID, strings.Join(actions, ";"))
			} else {
				fmt.Printf("%d;MESSAGE Waiting\n", my.AgentID)
			}
		}
	}
}
