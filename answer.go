package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
)

/**
 * Win the water fight by controlling the most territory, or out-soak your opponent!
 **/
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

type agent struct {
	//  fmt.Sscan(scanner.Text(),&agentId, &player, &shootCooldown, &optimalRange, &soakingPower, &splashBombs)
	AgentID       int
	Wetness       int
	PlayerID      int
	ShootCooldown int
	SoakingPower  int
	SplashBombs   int
	me            bool
	OptimalRange  int
	x, y          int
	Dead          bool
}

func (a agent) String() string {
	return fmt.Sprintf(
		"AgentID: %d\nWetness: %d\nPlayerID: %d\nShootCooldown: %d\nSoakingPower: %d\nSplashBombs: %d\nOptimalRange: %d\n x %d y %d\nMe: %v\n",
		a.AgentID, a.Wetness, a.PlayerID, a.<PERSON>ooldown, a.<PERSON>, a.<PERSON><PERSON><PERSON><PERSON>, a.<PERSON><PERSON><PERSON><PERSON>, a.x, a.y, a.me)
}

func main() {
	scanner := bufio.NewScanner(os.Stdin)
	scanner.Buffer(make([]byte, 1000000), 1000000)
	var inputs []string

	// myId: Your player id (0 or 1)
	var myId int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &myId)

	// agentDataCount: Total number of agents in the game
	var agentDataCount int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &agentDataCount)

	Agents := make([]agent, agentDataCount*2)

	for i := 0; i < agentDataCount; i++ {
		// agentId: Unique identifier for this agent
		// player: Player id of this agent
		// shootCooldown: Number of turns between each of this agent's shots
		// optimalRange: Maximum manhattan distance for greatest damage output
		// soakingPower: Damage output within optimal conditions
		// splashBombs: Number of splash bombs this can throw this game
		var agentId, player, shootCooldown, optimalRange, soakingPower, splashBombs int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentId, &player, &shootCooldown, &optimalRange, &soakingPower, &splashBombs)
		Agents[agentId] = agent{AgentID: agentId, me: myId == player, ShootCooldown: shootCooldown,
			SoakingPower: soakingPower,
			SplashBombs:  splashBombs, PlayerID: player, OptimalRange: optimalRange,
		}
		// fmt.Fprintln(os.Stderr, Agents[agentId])
	}

	// width: Width of the game map
	// height: Height of the game map
	var width, height int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &width, &height)

	for i := 0; i < height; i++ {
		scanner.Scan()
		inputs = strings.Split(scanner.Text(), " ")
		for j := 0; j < width; j++ {
			// x: X coordinate, 0 is left edge
			// y: Y coordinate, 0 is top edge
			x, _ := strconv.ParseInt(inputs[3*j], 10, 32)
			_ = x
			y, _ := strconv.ParseInt(inputs[3*j+1], 10, 32)
			_ = y
			tileType, _ := strconv.ParseInt(inputs[3*j+2], 10, 32)
			_ = tileType
		}
	}
	for {
		// agentCount: Total number of agents still in the game
		var agentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentCount)
		for i := 0; i < agentCount; i++ {
			// cooldown: Number of turns before this agent can shoot
			// wetness: Damage (0-100) this agent has taken
			var agentId, x, y, cooldown, splashBombs, wetness int
			scanner.Scan()
			fmt.Sscan(scanner.Text(), &agentId, &x, &y, &cooldown, &splashBombs, &wetness)
			Agents[agentId].x, Agents[agentId].y = x, y
			Agents[agentId].ShootCooldown = cooldown
			Agents[agentId].SplashBombs = splashBombs
			Agents[agentId].Wetness = wetness

			// fmt.Fprintln(os.Stderr, Agents[i])
		}

		// myAgentCount: Number of alive agents controlled by you
		var myAgentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &myAgentCount)

		myAgents := []agent{}
		for _, a := range Agents {
			if a.me {
				myAgents = append(myAgents, a)
			}
		}

		for i := 0; i < myAgentCount; i++ {
			my := myAgents[i]

			// Find the enemy with the highest wetness
			maxWetness := -1
			var target agent
			for _, enemy := range Agents {
				if !enemy.me && enemy.Wetness > maxWetness && enemy.Dead == false {
					maxWetness = enemy.Wetness
					target = enemy
				}
			}

			fmt.Printf("%d;MOVE 12 3;SHOOT %d\n", my.AgentID, target.AgentID)
			// fmt.Printf("%d;THROW %d\n",myAgents[(i+1) % myAgentCount].AgentID, target.AgentID)
			Agents[target.AgentID].Dead = true
			fmt.Fprintln(os.Stderr, target)
			fmt.Fprintln(os.Stderr, my)

			// One line per agent: <agentId>;<action1;action2;...> actions are "MOVE x y | SHOOT id | THROW x y | HUNKER_DOWN | MESSAGE text
		}
	}
}
