package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
)

/**
 * Win the water fight by controlling the most territory, or out-soak your opponent!
 **/
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func manhattanDistance(x1, y1, x2, y2 int) int {
	return abs(x1-x2) + abs(y1-y2)
}

type agent struct {
	AgentID       int
	Wetness       int
	PlayerID      int
	ShootCooldown int
	SoakingPower  int
	SplashBombs   int
	me            bool
	OptimalRange  int
	x, y          int
	cooldown      int // Current cooldown for this turn
}

func (a agent) String() string {
	return fmt.Sprintf(
		"AgentID: %d\nWetness: %d\nPlayerID: %d\nShootCooldown: %d\nSoakingPower: %d\nSplashBombs: %d\nOptimalRange: %d\n x %d y %d\nMe: %v\nCooldown: %d\n",
		a.<PERSON>ID, a.<PERSON>ness, a.<PERSON>, a.ShootCooldown, a.<PERSON>, a.<PERSON>, a.<PERSON><PERSON>, a.x, a.y, a.me, a.cooldown)
}

// Check if agent has splash bombs
func (a agent) hasSplashBombs() bool {
	return a.SplashBombs > 0
}

// Check if agent can shoot (cooldown is 0)
func (a agent) canShoot() bool {
	return a.cooldown == 0
}

// Calculate splash bomb damage (30 to target + 30 to all adjacent)
func calculateSplashDamage(targetX, targetY, agentX, agentY int) int {
	// Splash bombs deal 30 damage to target and all adjacent tiles
	return 30
}

// Check if position is within splash bomb range (max 4 tiles)
func isWithinSplashRange(fromX, fromY, toX, toY int) bool {
	distance := manhattanDistance(fromX, fromY, toX, toY)
	return distance <= 4
}

// Get all positions affected by splash bomb (target + 8 adjacent)
func getSplashAffectedPositions(targetX, targetY int) [][]int {
	positions := [][]int{{targetX, targetY}} // Target position

	// Add all 8 adjacent positions (orthogonal + diagonal)
	directions := [][]int{
		{-1, -1}, {-1, 0}, {-1, 1},
		{0, -1},           {0, 1},
		{1, -1},  {1, 0},  {1, 1},
	}

	for _, dir := range directions {
		positions = append(positions, []int{targetX + dir[0], targetY + dir[1]})
	}

	return positions
}

// Find best splash bomb target to hit most enemies without hitting friendlies
func findBestSplashTarget(myAgent agent, enemies []agent, friendlies []agent, width, height int) (int, int, int, bool) {
	bestX, bestY := -1, -1
	maxEnemiesHit := 0
	found := false

	// Try all positions within splash range
	for targetX := 0; targetX < width; targetX++ {
		for targetY := 0; targetY < height; targetY++ {
			if !isWithinSplashRange(myAgent.x, myAgent.y, targetX, targetY) {
				continue
			}

			affectedPositions := getSplashAffectedPositions(targetX, targetY)
			enemiesHit := 0
			friendliesHit := 0

			// Count enemies and friendlies that would be hit
			for _, pos := range affectedPositions {
				px, py := pos[0], pos[1]

				// Check enemies
				for _, enemy := range enemies {
					if enemy.x == px && enemy.y == py {
						enemiesHit++
					}
				}

				// Check friendlies
				for _, friendly := range friendlies {
					if friendly.x == px && friendly.y == py {
						friendliesHit++
					}
				}
			}

			// Only consider targets that don't hit friendlies and hit at least 1 enemy
			if friendliesHit == 0 && enemiesHit > maxEnemiesHit {
				maxEnemiesHit = enemiesHit
				bestX, bestY = targetX, targetY
				found = true
			}
		}
	}

	return bestX, bestY, maxEnemiesHit, found
}

// Find optimal position to move to for better splash bomb targeting
func findBestSplashPosition(myAgent agent, enemies []agent, friendlies []agent, gameMap [][]int, width, height int) (int, int, bool) {
	bestX, bestY := myAgent.x, myAgent.y
	maxEnemiesHit := 0
	found := false

	// Try moving to adjacent positions
	directions := [][]int{{0, 1}, {0, -1}, {1, 0}, {-1, 0}, {0, 0}} // up, down, right, left, stay
	for _, dir := range directions {
		newX, newY := myAgent.x+dir[0], myAgent.y+dir[1]

		// Check if position is valid
		if newX >= 0 && newX < width && newY >= 0 && newY < height && gameMap[newY][newX] == 0 {
			// Create temporary agent at new position
			tempAgent := myAgent
			tempAgent.x, tempAgent.y = newX, newY

			// Find best splash target from this position
			_, _, enemiesHit, canTarget := findBestSplashTarget(tempAgent, enemies, friendlies, width, height)

			if canTarget && enemiesHit > maxEnemiesHit {
				maxEnemiesHit = enemiesHit
				bestX, bestY = newX, newY
				found = true
			}
		}
	}

	return bestX, bestY, found
}

// Find enemy groups and return the smallest group's center
func findSmallestEnemyGroup(enemies []agent) (int, int, int) {
	if len(enemies) == 0 {
		return 0, 0, 0
	}

	// Group enemies by proximity (within 3 tiles of each other)
	groups := [][]agent{}
	used := make(map[int]bool)

	for _, enemy := range enemies {
		if used[enemy.AgentID] {
			continue
		}

		// Start a new group with this enemy
		group := []agent{enemy}
		used[enemy.AgentID] = true

		// Find all enemies close to this group
		for {
			groupExpanded := false
			for _, otherEnemy := range enemies {
				if used[otherEnemy.AgentID] {
					continue
				}

				// Check if this enemy is close to any enemy in the current group
				for _, groupMember := range group {
					if manhattanDistance(groupMember.x, groupMember.y, otherEnemy.x, otherEnemy.y) <= 3 {
						group = append(group, otherEnemy)
						used[otherEnemy.AgentID] = true
						groupExpanded = true
						break
					}
				}
				if groupExpanded {
					break
				}
			}
			if !groupExpanded {
				break
			}
		}

		groups = append(groups, group)
	}

	// Find the smallest group
	smallestGroup := groups[0]
	for _, group := range groups {
		if len(group) < len(smallestGroup) {
			smallestGroup = group
		}
	}

	// Calculate center of smallest group
	totalX, totalY := 0, 0
	for _, enemy := range smallestGroup {
		totalX += enemy.x
		totalY += enemy.y
	}

	centerX := totalX / len(smallestGroup)
	centerY := totalY / len(smallestGroup)

	return centerX, centerY, len(smallestGroup)
}

// Find the closest enemy group to an agent
func findClosestEnemyGroup(myAgent agent, enemies []agent) (int, int, int) {
	if len(enemies) == 0 {
		return myAgent.x, myAgent.y, 0
	}

	// Group enemies by proximity
	groups := [][]agent{}
	used := make(map[int]bool)

	for _, enemy := range enemies {
		if used[enemy.AgentID] {
			continue
		}

		group := []agent{enemy}
		used[enemy.AgentID] = true

		// Find nearby enemies
		for {
			groupExpanded := false
			for _, otherEnemy := range enemies {
				if used[otherEnemy.AgentID] {
					continue
				}
				for _, groupMember := range group {
					if manhattanDistance(groupMember.x, groupMember.y, otherEnemy.x, otherEnemy.y) <= 3 {
						group = append(group, otherEnemy)
						used[otherEnemy.AgentID] = true
						groupExpanded = true
						break
					}
				}
				if groupExpanded {
					break
				}
			}
			if !groupExpanded {
				break
			}
		}
		groups = append(groups, group)
	}

	// Find closest group to my agent
	closestGroup := groups[0]
	minDistance := 1000

	for _, group := range groups {
		// Calculate group center
		totalX, totalY := 0, 0
		for _, enemy := range group {
			totalX += enemy.x
			totalY += enemy.y
		}
		centerX := totalX / len(group)
		centerY := totalY / len(group)

		distance := manhattanDistance(myAgent.x, myAgent.y, centerX, centerY)
		if distance < minDistance {
			minDistance = distance
			closestGroup = group
		}
	}

	// Return center of closest group
	totalX, totalY := 0, 0
	for _, enemy := range closestGroup {
		totalX += enemy.x
		totalY += enemy.y
	}
	centerX := totalX / len(closestGroup)
	centerY := totalY / len(closestGroup)

	return centerX, centerY, len(closestGroup)
}

func main() {
	scanner := bufio.NewScanner(os.Stdin)
	scanner.Buffer(make([]byte, 1000000), 1000000)
	var inputs []string

	// myId: Your player id (0 or 1)
	var myId int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &myId)

	// agentDataCount: Total number of agents in the game
	var agentDataCount int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &agentDataCount)

	Agents := make(map[int]agent)

	for i := 0; i < agentDataCount; i++ {
		// agentId: Unique identifier for this agent
		// player: Player id of this agent
		// shootCooldown: Number of turns between each of this agent's shots
		// optimalRange: Maximum manhattan distance for greatest damage output
		// soakingPower: Damage output within optimal conditions
		// splashBombs: Number of splash bombs this can throw this game
		var agentId, player, shootCooldown, optimalRange, soakingPower, splashBombs int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentId, &player, &shootCooldown, &optimalRange, &soakingPower, &splashBombs)
		Agents[agentId] = agent{
			AgentID:       agentId,
			me:            myId == player,
			ShootCooldown: shootCooldown,
			SoakingPower:  soakingPower,
			SplashBombs:   splashBombs,
			PlayerID:      player,
			OptimalRange:  optimalRange,
		}
	}

	// width: Width of the game map
	// height: Height of the game map
	var width, height int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &width, &height)

	// Store map information (could be useful for advanced strategies)
	gameMap := make([][]int, height)
	for i := 0; i < height; i++ {
		gameMap[i] = make([]int, width)
		scanner.Scan()
		inputs = strings.Split(scanner.Text(), " ")
		for j := 0; j < width; j++ {
			x, _ := strconv.ParseInt(inputs[3*j], 10, 32)
			y, _ := strconv.ParseInt(inputs[3*j+1], 10, 32)
			tileType, _ := strconv.ParseInt(inputs[3*j+2], 10, 32)
			gameMap[int(y)][int(x)] = int(tileType)
		}
	}
	for {
		// agentCount: Total number of agents still in the game
		var agentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentCount)

		// Clear and rebuild the alive agents map
		aliveAgents := make(map[int]agent)

		// Update agent positions and states - only for agents that are still alive
		for i := 0; i < agentCount; i++ {
			var agentId, x, y, cooldown, splashBombs, wetness int
			scanner.Scan()
			fmt.Sscan(scanner.Text(), &agentId, &x, &y, &cooldown, &splashBombs, &wetness)

			if baseAgent, exists := Agents[agentId]; exists {
				// Update the agent with current turn data
				updatedAgent := baseAgent
				updatedAgent.x = x
				updatedAgent.y = y
				updatedAgent.cooldown = cooldown
				updatedAgent.SplashBombs = splashBombs
				updatedAgent.Wetness = wetness
				aliveAgents[agentId] = updatedAgent
			}
		}

		// myAgentCount: Number of alive agents controlled by you
		var myAgentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &myAgentCount)

		// Get my agents (only alive ones)
		myAgents := []agent{}
		for _, a := range aliveAgents {
			if a.me {
				myAgents = append(myAgents, a)
			}
		}

		// Get enemy agents (only alive ones)
		enemyAgents := []agent{}
		for _, a := range aliveAgents {
			if !a.me {
				enemyAgents = append(enemyAgents, a)
			}
		}

		// Debug: Log agent counts and positions
		fmt.Fprintf(os.Stderr, "Turn: MyAgents=%d, EnemyAgents=%d, TotalAlive=%d\n", len(myAgents), len(enemyAgents), len(aliveAgents))
		for _, a := range myAgents {
			fmt.Fprintf(os.Stderr, "MyAgent %d at (%d,%d) bombs:%d\n", a.AgentID, a.x, a.y, a.SplashBombs)
		}
		for _, a := range enemyAgents {
			fmt.Fprintf(os.Stderr, "Enemy %d at (%d,%d) wetness:%d\n", a.AgentID, a.x, a.y, a.Wetness)
		}

		// NEW STRATEGY: Use splash bombs to eliminate enemy groups
		// Shooting is disabled, only splash bombs work

		// Process each of my agents
		for i := 0; i < myAgentCount && i < len(myAgents); i++ {
			my := myAgents[i]
			var actions []string

			// SHOOTING IS DISABLED - Only splash bombs work!
			// Strategy: Use splash bombs efficiently to eliminate all enemy groups

			if my.hasSplashBombs() {
				// Step 1: Check if we can throw from current position first
				targetX, targetY, enemiesHit, canThrow := findBestSplashTarget(my, enemyAgents, myAgents, width, height)

				if canThrow && enemiesHit > 0 {
					// Can throw from current position
					actions = append(actions, fmt.Sprintf("THROW %d %d", targetX, targetY))
					actions = append(actions, fmt.Sprintf("MESSAGE Bomb->(%d,%d) hits:%d bombs:%d",
						targetX, targetY, enemiesHit, my.SplashBombs))
				} else {
					// Need to get closer - find the closest enemy group
					groupX, groupY, groupSize := findClosestEnemyGroup(my, enemyAgents)
					distance := manhattanDistance(my.x, my.y, groupX, groupY)

					// If we're far from the group, move closer aggressively
					if distance > 4 {
						// Move directly towards the group center
						moveX, moveY := my.x, my.y

						// Move both X and Y if possible (diagonal movement)
						if groupX > my.x && my.x+1 < width && gameMap[my.y][my.x+1] == 0 {
							moveX++
						} else if groupX < my.x && my.x-1 >= 0 && gameMap[my.y][my.x-1] == 0 {
							moveX--
						}

						if groupY > my.y && my.y+1 < height && gameMap[my.y+1][moveX] == 0 {
							moveY++
						} else if groupY < my.y && my.y-1 >= 0 && gameMap[my.y-1][moveX] == 0 {
							moveY--
						}

						if moveX != my.x || moveY != my.y {
							actions = append(actions, fmt.Sprintf("MOVE %d %d", moveX, moveY))
						}

						actions = append(actions, fmt.Sprintf("MESSAGE Approach group (%d,%d) size:%d dist:%d bombs:%d",
							groupX, groupY, groupSize, distance, my.SplashBombs))
					} else {
						// We're close, try to find optimal throwing position
						bestMoveX, bestMoveY, canMove := findBestSplashPosition(my, enemyAgents, myAgents, gameMap, width, height)

						if canMove && (bestMoveX != my.x || bestMoveY != my.y) {
							actions = append(actions, fmt.Sprintf("MOVE %d %d", bestMoveX, bestMoveY))
							actions = append(actions, fmt.Sprintf("MESSAGE Position for throw (%d,%d) bombs:%d",
								bestMoveX, bestMoveY, my.SplashBombs))
						} else {
							actions = append(actions, fmt.Sprintf("MESSAGE Ready to throw bombs:%d", my.SplashBombs))
						}
					}
				}
			} else {
				// No bombs left - try to get closer to enemies for next turn or support other agents
				if len(enemyAgents) > 0 {
					closest := enemyAgents[0]
					minDist := manhattanDistance(my.x, my.y, closest.x, closest.y)
					for _, enemy := range enemyAgents {
						dist := manhattanDistance(my.x, my.y, enemy.x, enemy.y)
						if dist < minDist {
							minDist = dist
							closest = enemy
						}
					}

					// Move towards closest enemy
					moveX, moveY := my.x, my.y
					if closest.x > my.x {
						moveX++
					} else if closest.x < my.x {
						moveX--
					}
					if closest.y > my.y {
						moveY++
					} else if closest.y < my.y {
						moveY--
					}

					if (moveX != my.x || moveY != my.y) && moveX >= 0 && moveX < width && moveY >= 0 && moveY < height && gameMap[moveY][moveX] == 0 {
						actions = append(actions, fmt.Sprintf("MOVE %d %d", moveX, moveY))
					}

					actions = append(actions, fmt.Sprintf("MESSAGE No bombs - approach E%d", closest.AgentID))
				} else {
					actions = append(actions, "MESSAGE Victory - no enemies left!")
				}
			}

			// Output the command
			if len(actions) > 0 {
				fmt.Printf("%d;%s\n", my.AgentID, strings.Join(actions, ";"))
			} else {
				fmt.Printf("%d;MESSAGE Waiting\n", my.AgentID)
			}
		}
	}
}
