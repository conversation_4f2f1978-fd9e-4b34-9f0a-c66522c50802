package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
)

/**
 * Win the water fight by controlling the most territory, or out-soak your opponent!
 **/
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func manhattanDistance(x1, y1, x2, y2 int) int {
	return abs(x1-x2) + abs(y1-y2)
}

type agent struct {
	AgentID       int
	Wetness       int
	PlayerID      int
	ShootCooldown int
	SoakingPower  int
	SplashBombs   int
	me            bool
	OptimalRange  int
	x, y          int
	cooldown      int // Current cooldown for this turn
}

func (a agent) String() string {
	return fmt.Sprintf(
		"AgentID: %d\nWetness: %d\nPlayerID: %d\nShootCooldown: %d\nSoakingPower: %d\nSplashBombs: %d\nOptimalRange: %d\n x %d y %d\nMe: %v\nCooldown: %d\n",
		a.<PERSON>ID, a.<PERSON>ness, a.<PERSON>ID, a.ShootCooldown, a.<PERSON>, a.<PERSON>, a.<PERSON>ti<PERSON><PERSON>, a.x, a.y, a.me, a.cooldown)
}

// Calculate effective damage at given distance with cover protection
func (a agent) calculateDamage(distance int, coverProtection float64) int {
	if distance > 2*a.OptimalRange {
		return 0 // Shot fails
	}
	baseDamage := a.SoakingPower
	if distance > a.OptimalRange {
		baseDamage = a.SoakingPower / 2 // Half damage beyond optimal range
	}
	// Apply cover protection
	finalDamage := float64(baseDamage) * (1.0 - coverProtection)
	return int(finalDamage)
}

// Check if agent can shoot (cooldown is 0)
func (a agent) canShoot() bool {
	return a.cooldown == 0
}

// Get cover protection percentage based on tile type
func getCoverProtection(tileType int) float64 {
	switch tileType {
	case 1: // Low cover
		return 0.5 // 50% protection
	case 2: // High cover
		return 0.75 // 75% protection
	default:
		return 0.0 // No protection
	}
}

// Check if position is orthogonally adjacent to a cover tile
func isAdjacentToCover(x, y int, gameMap [][]int, width, height int) (bool, int) {
	directions := [][]int{{0, 1}, {0, -1}, {1, 0}, {-1, 0}} // up, down, right, left
	maxCover := 0
	found := false

	for _, dir := range directions {
		nx, ny := x+dir[0], y+dir[1]
		if nx >= 0 && nx < width && ny >= 0 && ny < height {
			if gameMap[ny][nx] > 0 { // Cover tile
				found = true
				if gameMap[ny][nx] > maxCover {
					maxCover = gameMap[ny][nx]
				}
			}
		}
	}
	return found, maxCover
}

// Find best cover position for an agent
func findBestCoverPosition(agent agent, gameMap [][]int, width, height int) (int, int, int) {
	bestX, bestY := agent.x, agent.y
	bestCover := 0

	// Check all positions within 1 move
	for dx := -1; dx <= 1; dx++ {
		for dy := -1; dy <= 1; dy++ {
			if abs(dx) + abs(dy) != 1 { // Only orthogonal moves
				continue
			}
			newX, newY := agent.x+dx, agent.y+dy
			if newX >= 0 && newX < width && newY >= 0 && newY < height && gameMap[newY][newX] == 0 {
				// Check if this position is adjacent to cover
				if adjacent, coverType := isAdjacentToCover(newX, newY, gameMap, width, height); adjacent {
					if coverType > bestCover {
						bestCover = coverType
						bestX, bestY = newX, newY
					}
				}
			}
		}
	}
	return bestX, bestY, bestCover
}

func main() {
	scanner := bufio.NewScanner(os.Stdin)
	scanner.Buffer(make([]byte, 1000000), 1000000)
	var inputs []string

	// myId: Your player id (0 or 1)
	var myId int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &myId)

	// agentDataCount: Total number of agents in the game
	var agentDataCount int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &agentDataCount)

	Agents := make(map[int]agent)

	for i := 0; i < agentDataCount; i++ {
		// agentId: Unique identifier for this agent
		// player: Player id of this agent
		// shootCooldown: Number of turns between each of this agent's shots
		// optimalRange: Maximum manhattan distance for greatest damage output
		// soakingPower: Damage output within optimal conditions
		// splashBombs: Number of splash bombs this can throw this game
		var agentId, player, shootCooldown, optimalRange, soakingPower, splashBombs int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentId, &player, &shootCooldown, &optimalRange, &soakingPower, &splashBombs)
		Agents[agentId] = agent{
			AgentID:       agentId,
			me:            myId == player,
			ShootCooldown: shootCooldown,
			SoakingPower:  soakingPower,
			SplashBombs:   splashBombs,
			PlayerID:      player,
			OptimalRange:  optimalRange,
		}
	}

	// width: Width of the game map
	// height: Height of the game map
	var width, height int
	scanner.Scan()
	fmt.Sscan(scanner.Text(), &width, &height)

	// Store map information (could be useful for advanced strategies)
	gameMap := make([][]int, height)
	for i := 0; i < height; i++ {
		gameMap[i] = make([]int, width)
		scanner.Scan()
		inputs = strings.Split(scanner.Text(), " ")
		for j := 0; j < width; j++ {
			x, _ := strconv.ParseInt(inputs[3*j], 10, 32)
			y, _ := strconv.ParseInt(inputs[3*j+1], 10, 32)
			tileType, _ := strconv.ParseInt(inputs[3*j+2], 10, 32)
			gameMap[int(y)][int(x)] = int(tileType)
		}
	}
	for {
		// agentCount: Total number of agents still in the game
		var agentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &agentCount)

		// Clear and rebuild the alive agents map
		aliveAgents := make(map[int]agent)

		// Update agent positions and states - only for agents that are still alive
		for i := 0; i < agentCount; i++ {
			var agentId, x, y, cooldown, splashBombs, wetness int
			scanner.Scan()
			fmt.Sscan(scanner.Text(), &agentId, &x, &y, &cooldown, &splashBombs, &wetness)

			if baseAgent, exists := Agents[agentId]; exists {
				// Update the agent with current turn data
				updatedAgent := baseAgent
				updatedAgent.x = x
				updatedAgent.y = y
				updatedAgent.cooldown = cooldown
				updatedAgent.SplashBombs = splashBombs
				updatedAgent.Wetness = wetness
				aliveAgents[agentId] = updatedAgent
			}
		}

		// myAgentCount: Number of alive agents controlled by you
		var myAgentCount int
		scanner.Scan()
		fmt.Sscan(scanner.Text(), &myAgentCount)

		// Get my agents (only alive ones)
		myAgents := []agent{}
		for _, a := range aliveAgents {
			if a.me {
				myAgents = append(myAgents, a)
			}
		}

		// Get enemy agents (only alive ones)
		enemyAgents := []agent{}
		for _, a := range aliveAgents {
			if !a.me {
				enemyAgents = append(enemyAgents, a)
			}
		}

		// Debug: Log agent counts
		fmt.Fprintf(os.Stderr, "Turn: MyAgents=%d, EnemyAgents=%d, TotalAlive=%d\n", len(myAgents), len(enemyAgents), len(aliveAgents))

		// NEW STRATEGY: Move to best cover and shoot enemy with least protection
		// Process each of my agents
		for i := 0; i < myAgentCount && i < len(myAgents); i++ {
			my := myAgents[i]
			var actions []string

			// Step 1: Find best cover position for this agent
			bestX, bestY, bestCover := findBestCoverPosition(my, gameMap, width, height)

			// Step 2: Find enemy with least protection from cover
			var target agent
			minProtection := 1.0 // Start with maximum protection
			found := false

			for _, enemy := range enemyAgents {
				if _, exists := aliveAgents[enemy.AgentID]; exists {
					// Check enemy's cover protection
					adjacent, coverType := isAdjacentToCover(enemy.x, enemy.y, gameMap, width, height)
					protection := 0.0
					if adjacent {
						protection = getCoverProtection(coverType)
					}

					// Find enemy with least protection
					if protection < minProtection {
						minProtection = protection
						target = enemy
						found = true
					}
				}
			}

			if !found {
				fmt.Printf("%d;MESSAGE No valid targets\n", my.AgentID)
				continue
			}

			// Step 3: Move to best cover position (if different from current)
			if bestX != my.x || bestY != my.y {
				actions = append(actions, fmt.Sprintf("MOVE %d %d", bestX, bestY))
			}

			// Step 4: Shoot the target with least protection
			if my.canShoot() {
				// Calculate distance from new position to target
				distance := manhattanDistance(bestX, bestY, target.x, target.y)
				if distance <= 2*my.OptimalRange {
					actions = append(actions, fmt.Sprintf("SHOOT %d", target.AgentID))
				}
			}

			// Debug message
			coverProtection := getCoverProtection(bestCover)
			targetProtection := minProtection
			actions = append(actions, fmt.Sprintf("MESSAGE Cover:%d(%.0f%%) Target:%d(%.0f%%)",
				bestCover, coverProtection*100, target.AgentID, targetProtection*100))

			// Output the command
			if len(actions) > 0 {
				fmt.Printf("%d;%s\n", my.AgentID, strings.Join(actions, ";"))
			} else {
				fmt.Printf("%d;MESSAGE No actions\n", my.AgentID)
			}
		}
	}
}
